#!/bin/bash
set -euo pipefail

# 构建并推送镜像
docker build --no-cache -t lkeke/box777-admin:test . || exit 1
docker push lkeke/box777-admin:test || exit 1

# 远程部署服务
REMOTE_HOST="box-777-test"
REMOTE_PATH="/opt/docker-compose/box777-admin"

echo "🚀 部署到 $REMOTE_HOST"
ssh ubuntu@$REMOTE_HOST "
    cd $REMOTE_PATH || exit 1
    docker-compose down || true
    docker-compose pull || exit 1
    docker-compose up -d || exit 1
" || exit 1

echo "✅ 部署完成"