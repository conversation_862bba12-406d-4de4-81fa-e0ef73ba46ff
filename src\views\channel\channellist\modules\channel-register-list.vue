<template>
  <div class="channel-register-list">
    <el-form :inline="true" class="mb-2">
      <el-form-item label="用户ID">
        <el-input v-model="search.userId" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="search.time"
          type="daterange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="x"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      max-height="400"
      border
    >
      <el-table-column prop="id" label="序号" width="60" />
      <el-table-column prop="user_id" label="用户ID" />
      <el-table-column prop="nickname" label="用户昵称" />
      <el-table-column prop="phone" label="手机号" />
      <el-table-column prop="register_time" label="注册时间" >
        <template #default="{ row }">
          {{ moment(getBrazilDate(row.register_time)).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { fetchGetChannelRegisters } from "@/service/api/channel";
import moment from "moment";
import { getBrazilDate } from "@/utils/format";

const props = defineProps<{
  channel: any;
  loading?: boolean;
}>();

const emit = defineEmits(['refresh']);

const search = ref({
  userId: '',
  time: []
});

const tableData = ref<any[]>([]);
const loading = ref(false);
const page = ref(1);
const pageSize = ref(10);
const total = ref(0);

const loadData = async () => {
  if (!props.channel?.channel_code) return;

  loading.value = true;
  try {
    const response = await fetchGetChannelRegisters({
      channel_code: props.channel.channel_code,
      page: page.value,
      size: pageSize.value,
      userId: search.value.userId,
      startTime: search.value.time?.[0],
      endTime: search.value.time?.[1]
    });
    if (response?.data) {
      tableData.value = response.data.data;
      total.value = response.data.count;
    }
  } catch (error) {
    console.error('加載註冊清單失敗:', error);
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  page.value = 1;
  loadData();
};

const handleReset = () => {
  search.value = {
    userId: '',
    time: []
  };
  handleSearch();
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  page.value = 1;
  loadData();
};

const handleCurrentChange = (val: number) => {
  page.value = val;
  loadData();
};



defineExpose({
  loadData
});
</script>

<style scoped>
.channel-register-list {
  padding: 20px 0;
}

.mb-2 {
  margin-bottom: 20px;
  /* display:flex; */
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
