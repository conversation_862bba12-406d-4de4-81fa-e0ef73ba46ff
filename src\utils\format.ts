/**
 * 格式化工具类
 */

/**
 * 获取巴西时间的辅助函数
 * @returns 巴西时间的Date对象
 */
export const getBrazilTime = (): Date => {
  return new Date(new Date().toLocaleString("en-US", {timeZone: "America/Sao_Paulo"}));
};

/**
 * 将日期字符串转换为巴西时间的Date对象
 * @param dateString 日期字符串或时间戳
 * @returns 巴西时间的Date对象
 */
export const getBrazilDate = (dateString: string | number): Date => {
  const date = new Date(dateString);
  // 将UTC时间转换为巴西时间
  const brazilTime = new Date(date.toLocaleString("en-US", {timeZone: "America/Sao_Paulo"}));
  return brazilTime;
};
