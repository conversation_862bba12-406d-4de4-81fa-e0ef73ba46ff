import { DateFormat } from "./types";
import { getBrazilTime, getBrazilDate } from "./format";

/**
 * 日期处理工具类
 */

/**
 * 日期格式化工具
 */

/**
 * 格式化日期
 * @param dateString 日期字符串或时间戳
 * @param format 格式化模式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  dateString: string | number,
  format: string = "YYYY-MM-DD HH:mm:ss"
): string {
  if (!dateString) return "";

  const date = getBrazilDate(dateString);

  if (isNaN(date.getTime())) {
    return "";
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return year +"-"+ month +"-"+ day+" "+ hours +":"+ minutes +":"+ seconds;
}

/**
 * 获取相对时间
 * @param dateString 日期字符串或时间戳
 * @returns 相对时间字符串，如 "刚刚"、"5分钟前"、"2小时前"、"3天前" 等
 */
export function getRelativeTime(dateString: string | number): string {
  if (!dateString) return "";

  const date = getBrazilDate(dateString);

  if (isNaN(date.getTime())) {
    return "";
  }

  const now = getBrazilTime();
  const diff = now.getTime() - date.getTime();

  // 小于1分钟
  if (diff < 60 * 1000) {
    return "Agora mesmo";
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000));
    return `${minutes} ${minutes === 1 ? "minuto" : "minutos"} atrás`;
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000));
    return `${hours} ${hours === 1 ? "hora" : "horas"} atrás`;
  }

  // 小于1周
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    return `${days} ${days === 1 ? "dia" : "dias"} atrás`;
  }

  // 小于1个月
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    const weeks = Math.floor(diff / (7 * 24 * 60 * 60 * 1000));
    return `${weeks} ${weeks === 1 ? "semana" : "semanas"} atrás`;
  }

  // 小于1年
  if (diff < 365 * 24 * 60 * 60 * 1000) {
    const months = Math.floor(diff / (30 * 24 * 60 * 60 * 1000));
    return `${months} ${months === 1 ? "mês" : "meses"} atrás`;
  }

  // 大于1年
  const years = Math.floor(diff / (365 * 24 * 60 * 60 * 1000));
  return `${years} ${years === 1 ? "ano" : "anos"} atrás`;
}
